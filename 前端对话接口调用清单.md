# 前端对话接口调用清单

## 核心对话流程

### 1. 会话初始化
```
场景：用户首次开始对话或开启新对话
接口：POST /humanrelation/create_conversation
参数：{"user_id": "string"}
返回：{"success": true, "conversation_id": "user123_2024-01-01-10-30-45"}
```

### 2. 对话接口（二选一）

#### 2.1 流式对话（推荐）
```
场景：实时显示AI回复，用户体验更好
接口：POST /humanrelation/chat
参数：{
  "content": "用户消息内容",
  "conversation_id": "会话ID",
  "user_id": "用户ID"
}
返回：SSE流式响应
Content-Type: text/event-stream
data: {"content": "AI回复片段", "type": "chat"}
data: {"type": "end"}
```

#### 2.2 非流式对话
```
场景：简单实现，一次性返回完整回复
接口：POST /humanrelation/chat_json
参数：{
  "content": "用户消息内容", 
  "conversation_id": "会话ID",
  "user_id": "用户ID"
}
返回：{"response": "AI完整回复", "status": "success"}
```

### 3. 历史记录查看
```
场景：查看某个对话的完整聊天记录
接口：GET /humanrelation/history?conversation_id=xxx&user_id=xxx
返回：{
  "conversation_id": "会话ID",
  "history": [消息列表],
  "status": "success"
}
```

## 后台自动处理（无需前端调用）

对话接口会自动触发以下处理：
1. **用户档案检查** - 确保用户本人档案存在
2. **记忆提取** - 从对话中自动提取人物和事件信息
3. **记忆存储** - 将提取的信息存储到数据库
4. **上下文检索** - 检索相关历史记忆为对话提供上下文
5. **智能回复** - 基于记忆和上下文生成个性化回复

## 可选管理功能

### 1. 会话管理
```
获取用户所有对话列表：
GET /humanrelation/conversations?user_id=xxx
返回：{
  "success": true,
  "user_id": "user123", 
  "conversation_ids": ["会话ID1", "会话ID2"]
}
```

### 2. 人员管理
```
查看所有人员：
GET /humanrelation/persons?user_id=xxx&limit=100&offset=0

搜索特定人员：
GET /humanrelation/search_person?user_id=xxx&name=张三&limit=10

查看某个人详情：
GET /humanrelation/person/{person_id}?user_id=xxx
```

### 3. 记忆管理
```
搜索记忆：
POST /humanrelation/search_memory
{
  "user_input": "搜索关键词",
  "size": 10,
  "k": 20,
  "user_id": "用户ID",
  "memory_type": "long" // 或 "short"
}

手动添加记忆：
POST /humanrelation/add_memory
{
  "conversation_text": "要处理的对话文本",
  "user_id": "用户ID"
}
```

## 前端实现建议

### 1. 状态管理
```javascript
// 前端需要维护的状态
const chatState = {
  userId: "当前用户ID",
  conversationId: "当前会话ID", 
  messages: [], // 消息历史
  isStreaming: false // 是否正在接收流式响应
}
```

### 2. 流式响应处理
```javascript
// SSE流式响应处理示例
const eventSource = new EventSource('/humanrelation/chat', {
  method: 'POST',
  body: JSON.stringify(requestData)
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'chat') {
    // 更新UI显示AI回复
    updateChatDisplay(data.content);
  } else if (data.type === 'end') {
    // 流式响应结束
    eventSource.close();
  }
};
```

### 3. 错误处理
```javascript
// 统一错误处理
const handleApiError = (error) => {
  console.error('API调用失败:', error);
  // 显示用户友好的错误信息
  showErrorMessage('抱歉，服务暂时不可用，请稍后重试');
};
```

## 接口调用顺序

### 新用户首次对话
1. `POST /humanrelation/create_conversation` - 创建会话ID
2. `POST /humanrelation/chat` 或 `chat_json` - 开始对话
3. （可选）`GET /humanrelation/history` - 查看历史

### 老用户继续对话
1. 使用已有的 `conversation_id`
2. 直接调用 `POST /humanrelation/chat` 或 `chat_json`

### 查看历史和管理
1. `GET /humanrelation/conversations` - 获取对话列表
2. `GET /humanrelation/history` - 查看特定对话历史
3. 其他管理接口按需调用

## 注意事项

1. **用户ID必须一致** - 所有接口调用都要使用相同的user_id
2. **会话ID格式** - 系统生成格式为 `{user_id}_{timestamp}`
3. **自动处理** - 对话接口会自动处理用户档案和记忆，无需额外调用
4. **错误重试** - 建议对网络错误进行重试机制
5. **流式响应** - 推荐使用流式接口提升用户体验
