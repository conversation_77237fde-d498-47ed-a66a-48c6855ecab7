<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="前端对话接口调用流程" id="frontend-chat-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 开始节点 -->
        <mxCell id="start" value="用户开始对话" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 判断是否有conversation_id -->
        <mxCell id="hasConvId" value="是否有&lt;br&gt;conversation_id?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 创建新会话ID -->
        <mxCell id="createConv" value="调用 POST&lt;br&gt;/humanrelation/&lt;br&gt;create_conversation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="140" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 获取新conversation_id -->
        <mxCell id="getNewId" value="获取新的&lt;br&gt;conversation_id" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="260" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 存储到前端状态 -->
        <mxCell id="storeId" value="存储conversation_id&lt;br&gt;到前端状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="360" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 使用现有ID -->
        <mxCell id="useExisting" value="使用现有&lt;br&gt;conversation_id" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 用户输入消息 -->
        <mxCell id="userInput" value="用户输入消息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 选择对话模式 -->
        <mxCell id="chatMode" value="选择对话模式" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="560" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 流式对话 -->
        <mxCell id="streamChat" value="调用 POST&lt;br&gt;/humanrelation/chat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="520" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 非流式对话 -->
        <mxCell id="jsonChat" value="调用 POST&lt;br&gt;/humanrelation/&lt;br&gt;chat_json" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="620" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- SSE流式响应 -->
        <mxCell id="sseResponse" value="接收SSE&lt;br&gt;流式响应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- JSON响应 -->
        <mxCell id="jsonResponse" value="接收JSON响应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 实时显示 -->
        <mxCell id="realTimeDisplay" value="实时显示&lt;br&gt;AI回复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 显示完整回复 -->
        <mxCell id="fullDisplay" value="显示完整&lt;br&gt;AI回复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 对话结束 -->
        <mxCell id="chatEnd" value="对话结束" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="720" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 是否继续对话 -->
        <mxCell id="continueChat" value="用户是否&lt;br&gt;继续对话?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="720" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 后台自动处理 -->
        <mxCell id="autoProcess" value="后台自动处理&lt;br&gt;(用户档案检查&lt;br&gt;记忆提取存储&lt;br&gt;上下文构建)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;strokeWidth=2;strokeDashArray=5 5;" vertex="1" parent="1">
          <mxGeometry x="880" y="560" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" edge="1" parent="1" source="start" target="hasConvId">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="hasConvId" target="createConv">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge2-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge2">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="hasConvId" target="useExisting">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge3-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge3">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="createConv" target="getNewId">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="getNewId" target="storeId">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="storeId" target="userInput">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="350" y="440" />
              <mxPoint x="140" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="useExisting" target="userInput">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="userInput" target="chatMode">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="chatMode" target="streamChat">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge9-label" value="流式" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge9">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="chatMode" target="jsonChat">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge10-label" value="非流式" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge10">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge11" edge="1" parent="1" source="streamChat" target="sseResponse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" edge="1" parent="1" source="jsonChat" target="jsonResponse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" edge="1" parent="1" source="sseResponse" target="realTimeDisplay">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge14" edge="1" parent="1" source="jsonResponse" target="fullDisplay">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge15" edge="1" parent="1" source="realTimeDisplay" target="chatEnd">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge16" edge="1" parent="1" source="fullDisplay" target="chatEnd">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge17" edge="1" parent="1" source="chatEnd" target="continueChat">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge18" edge="1" parent="1" source="continueChat" target="userInput">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="540" y="820" />
              <mxPoint x="40" y="820" />
              <mxPoint x="40" y="490" />
            </Array>
          </mxGeometry>
          <mxCell id="edge18-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge18">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <!-- 会话结束 -->
        <mxCell id="sessionEnd" value="对话会话结束" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="720" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 是否查看历史 -->
        <mxCell id="viewHistory" value="是否需要查看&lt;br&gt;历史记录?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="720" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- 获取历史记录 -->
        <mxCell id="getHistory" value="调用 GET&lt;br&gt;/humanrelation/history" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="840" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 显示历史记录 -->
        <mxCell id="showHistory" value="显示完整&lt;br&gt;聊天记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="840" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 管理功能选择 -->
        <mxCell id="managementChoice" value="是否需要&lt;br&gt;管理功能?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="840" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- 查看所有对话 -->
        <mxCell id="getAllConv" value="调用 GET&lt;br&gt;/humanrelation/&lt;br&gt;conversations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="760" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 查看人员列表 -->
        <mxCell id="getPersons" value="调用 GET&lt;br&gt;/humanrelation/persons" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="840" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 搜索记忆 -->
        <mxCell id="searchMemory" value="调用 POST&lt;br&gt;/humanrelation/&lt;br&gt;search_memory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="920" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 手动添加记忆 -->
        <mxCell id="addMemory" value="调用 POST&lt;br&gt;/humanrelation/&lt;br&gt;add_memory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="680" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 结束流程 -->
        <mxCell id="endProcess" value="结束流程" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="1000" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 后台处理连接线 -->
        <mxCell id="edge19" edge="1" parent="1" source="streamChat" target="autoProcess">
          <mxGeometry relative="1" as="geometry">
            <mxCell id="edge19-label" value="自动触发" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontStyle=2;" vertex="1" connectable="0" parent="edge19">
              <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
            </mxCell>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge20" edge="1" parent="1" source="jsonChat" target="autoProcess">
          <mxGeometry relative="1" as="geometry">
            <mxCell id="edge20-label" value="自动触发" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontStyle=2;" vertex="1" connectable="0" parent="edge20">
              <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
            </mxCell>
          </mxGeometry>
        </mxCell>

        <!-- 新增连接线 -->
        <mxCell id="edge21" edge="1" parent="1" source="continueChat" target="sessionEnd">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge21-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge21">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge22" edge="1" parent="1" source="sessionEnd" target="viewHistory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge23" edge="1" parent="1" source="viewHistory" target="getHistory">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge23-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge23">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge24" edge="1" parent="1" source="getHistory" target="showHistory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge25" edge="1" parent="1" source="showHistory" target="managementChoice">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge26" edge="1" parent="1" source="viewHistory" target="managementChoice">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="140" y="880" />
              <mxPoint x="540" y="880" />
            </Array>
          </mxGeometry>
          <mxCell id="edge26-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge26">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge27" edge="1" parent="1" source="managementChoice" target="getAllConv">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge27-label" value="查看对话" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge27">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge28" edge="1" parent="1" source="managementChoice" target="getPersons">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge28-label" value="查看人员" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge28">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge29" edge="1" parent="1" source="managementChoice" target="searchMemory">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge29-label" value="搜索记忆" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge29">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge30" edge="1" parent="1" source="managementChoice" target="addMemory">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge30-label" value="添加记忆" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge30">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge31" edge="1" parent="1" source="managementChoice" target="endProcess">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge31-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge31">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge32" edge="1" parent="1" source="getAllConv" target="endProcess">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="1030" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge33" edge="1" parent="1" source="getPersons" target="endProcess">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="1030" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge34" edge="1" parent="1" source="searchMemory" target="endProcess">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="1030" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge35" edge="1" parent="1" source="addMemory" target="endProcess">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="1030" />
            </Array>
          </mxGeometry>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
